<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 知识图谱管理系统</title>
    <!-- Bootstrap CSS (你可以替换为本地路径或项目配置的路径) -->
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f8f9fa;
        }
        .login-container {
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        .login-container h2 {
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2>用户登录</h2>
        {% if form.errors %}
            <div class="alert alert-danger">
                您的用户名和密码不匹配。请重试。
            </div>
        {% endif %}
        <form method="post">
            {% csrf_token %}
            {% for field in form %}
                <div class="form-group">
                    {{ field.label_tag }}
                    <input type="{{ field.field.widget.input_type }}" 
                           name="{{ field.name }}" 
                           id="{{ field.id_for_label }}" 
                           class="form-control" 
                           placeholder="{{ field.label }}">
                    {% if field.help_text %}
                        <small class="form-text text-muted">{{ field.help_text }}</small>
                    {% endif %}
                    {% for error in field.errors %}
                        <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                </div>
            {% endfor %}
            <button type="submit" class="btn btn-primary btn-block">登录</button>
        </form>
        <p class="mt-3 text-center">还没有账号？<a href="{% url 'register' %}">立即注册</a></p>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html> 