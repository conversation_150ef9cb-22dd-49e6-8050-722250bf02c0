# Generated by Django 4.2.21 on 2025-05-23 11:29

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import taggit.managers
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('taggit', '0006_rename_taggeditem_content_type_object_id_taggit_tagg_content_8fc721_idx'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='KnowledgeItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=500, unique=True, verbose_name='名称/标题')),
                ('item_type', models.CharField(choices=[('分类', '分类'), ('知识点', '知识点')], max_length=10, verbose_name='条目类型')),
                ('content', models.TextField(blank=True, null=True, verbose_name='内容/描述')),
                ('csv_predecessor_nodes_str', models.TextField(blank=True, null=True, verbose_name='CSV前置节点原始字符串')),
                ('csv_successor_nodes_str', models.TextField(blank=True, null=True, verbose_name='CSV后置节点原始字符串')),
                ('csv_related_nodes_str', models.TextField(blank=True, null=True, verbose_name='CSV关联节点原始字符串')),
                ('tags_str', models.CharField(blank=True, max_length=500, null=True, verbose_name='标签（CSV原始）')),
                ('csv_knowledge_point_category', models.CharField(blank=True, max_length=255, null=True, verbose_name='知识点分类（CSV原始）')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='knowledge_graph.knowledgeitem', verbose_name='父级分类')),
                ('predecessor_nodes', models.ManyToManyField(blank=True, related_name='successor_of', to='knowledge_graph.knowledgeitem', verbose_name='前置节点')),
                ('related_nodes', models.ManyToManyField(blank=True, to='knowledge_graph.knowledgeitem', verbose_name='关联节点')),
                ('successor_nodes', models.ManyToManyField(blank=True, related_name='predecessor_of', to='knowledge_graph.knowledgeitem', verbose_name='后置节点')),
            ],
            options={
                'verbose_name': '知识条目',
                'verbose_name_plural': '知识条目',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='StudyNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='笔记标题')),
                ('content', models.TextField(verbose_name='笔记内容')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_public', models.BooleanField(default=False, verbose_name='是否公开')),
                ('share_slug', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='分享链接ID')),
                ('related_knowledge_item', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='study_notes', to='knowledge_graph.knowledgeitem', verbose_name='关联知识点')),
                ('tags', taggit.managers.TaggableManager(blank=True, help_text='A comma-separated list of tags.', through='taggit.TaggedItem', to='taggit.Tag', verbose_name='标签')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='study_notes', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '学习笔记',
                'verbose_name_plural': '学习笔记',
                'ordering': ['-updated_at'],
            },
        ),
    ]
