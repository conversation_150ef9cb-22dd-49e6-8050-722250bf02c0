{% extends "knowledge_graph/base.html" %}
{% load i18n %}

{% block title %}{{ note.title }} - 学习笔记 - {{ block.super }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'study_note_list' %}">我的学习笔记</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ note.title }}</li>
        </ol>
    </nav>

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h1 class="section-title mb-0">{{ note.title }}</h1>
            <div>
                {% if note.user == request.user %}
                <a href="{% url 'study_note_update' pk=note.pk %}" class="btn btn-sm btn-outline-primary">编辑</a>
                <a href="{% url 'study_note_delete' pk=note.pk %}" class="btn btn-sm btn-outline-danger">删除</a>
                {% endif %}
            </div>
        </div>
        <div class="card-body">
            <p class="card-text"><small class="text-muted">最后更新: {{ note.updated_at|date:"Y年m月d日 H:i" }} ({{ note.updated_at|timesince }}前)</small></p>
            
            {% if note.user == request.user %}
                {% if note.is_public %}
                    <div class="alert alert-success" role="alert">
                        这篇笔记是公开的。分享链接: 
                        <input type="text" readonly class="form-control-plaintext form-control-sm d-inline w-auto" value="{{ request.scheme }}://{{ request.get_host }}{% url 'public_note_detail' share_slug=note.share_slug %}" id="shareLink-{{note.pk}}">
                        <button class="btn btn-sm btn-outline-secondary ml-1" onclick="copyLink('shareLink-{{note.pk}}')">复制</button>
                    </div>
                {% else %}
                    <div class="alert alert-info" role="alert">
                        这篇笔记是私有的。你可以 <a href="{% url 'study_note_update' pk=note.pk %}">编辑笔记</a> 将其设为公开。
                    </div>
                {% endif %}
            {% endif %}

            {% if note.related_knowledge_item %}
                <p class="card-text">
                    <strong>关联知识点:</strong> 
                    <a href="{% url 'home' %}?q={{ note.related_knowledge_item.name|urlencode }}">{{ note.related_knowledge_item.name }}</a>
                </p>
            {% endif %}
            {% if note.tags.all %}
                <p class="card-text">
                    <strong>标签:</strong>
                    {% for tag in note.tags.all %}
                        <a href="{% url 'study_note_list_by_tag' tag_slug=tag.slug %}" class="badge badge-info mr-1">{{ tag.name }}</a>
                    {% endfor %}
                </p>
            {% endif %}
            <hr>
            <div class="note-content">
                {{ note.content|safe }}
            </div>
        </div>
        <div class="card-footer text-muted">
            创建时间: {{ note.created_at|date:"Y年m月d日 H:i" }}
        </div>
    </div>

    <div class="mt-3">
        <a href="{% url 'study_note_list' %}" class="btn btn-secondary">返回笔记列表</a>
    </div>
</div>

{# Toast HTML for copy notification #}
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1100">
  <div id="copyToast" class="toast hide" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="2000">
    <div class="toast-header bg-success text-white">
      <strong class="me-auto">提示</strong>
      <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
    <div class="toast-body">
      分享链接已复制到剪贴板！
    </div>
  </div>
</div>

{% endblock %}

{% block extra_scripts %}
<script>
function copyLink(elementId) {
  var copyText = document.getElementById(elementId);
  copyText.select();
  copyText.setSelectionRange(0, 99999); /* For mobile devices */
  
  navigator.clipboard.writeText(copyText.value).then(function() {
    // Clipboard successfully set
    var toastEl = document.getElementById('copyToast');
    var toast = new bootstrap.Toast(toastEl);
    toast.show();
  }, function() {
    // Clipboard write failed
    alert("复制失败，请手动复制。"); // Fallback for older browsers or if permission denied
  });
}
</script>
{% endblock %} 