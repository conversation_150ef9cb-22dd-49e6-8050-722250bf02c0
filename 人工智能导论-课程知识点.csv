﻿节点类型,节点名称,节点名称,节点名称,节点名称,节点名称,节点名称,节点名称,前置节点,后置节点,关联节点,标签,知识点分类,节点说明
知识点,人工智能导论,,,,,,,,,,,,
分类,知识与推理,,,,,,,,,,,,
分类,,基于产生式规则的机器推理,,,,,,,,,,,
分类,,,产生式规则,,,,,,,,,,
分类,,,,产生式规则与推理网络,,,,,,,,,
分类,,,,基于产生式规则的推理模式,,,,,,,,,
分类,,,产生式系统,,,,,,,,,,
分类,,,,产生式系统结构,,,,,,,,,
分类,,,,产生式系统运行过程,,,,,,,,,
分类,,,,控制策略与常用算法,,,,,,,,,
分类,,,,程序实现,,,,,,,,,
分类,,,产生式系统与图搜索问题求解,,,,,,,,,,
分类,,几种结构化知识表示及其推理,,,,,,,,,,,
分类,,,元组,,,,,,,,,,
分类,,,框架,,,,,,,,,,
分类,,,语义网络,,,,,,,,,,
分类,,,知识图谱,,,,,,,,,,
分类,,,类与对象,,,,,,,,,,
分类,,不确定和不确切性知识的表示与推理,,,,,,,,,,,
分类,,,不确定性知识的表示及推理,,,,,,,,,,
分类,,,几种经典的不确定性推理模型,,,,,,,,,,
分类,,,基于贝叶斯网络的概率推理,,,,,,,,,,
分类,,,不确切性知识的表示及推理,,,,,,,,,,
分类,,基于一阶谓词的机器推理,,,,,,,,,,,
分类,,,一阶谓词逻辑,,,,,,,,,,
分类,,,,谓词，函数，量词,,,,,,,,,
知识点,,,,,"表达式 P(t₁,t₂,…,tn) 称为一个n 元谓词，或简称谓词。其中，P 是谓词名或谓词符号，也称谓词，表示对象的 属性、状态、关系、联系或行为；t₁,t₂, …,t,       称为谓词的项，一般代表对象。",,,,,,,,
知识点,,,,,个体常元,,,,,,,,
知识点,,,,,个体变元,,,,,,,,
知识点,,,,,命题,,,,,,,,
知识点,,,,,命题形式,,,,,,,,
知识点,,,,,个体域(或论述域),,,,,,,,
知识点,,,,,全称量词,,,,,,,,
知识点,,,,,存在量词,,,,,,,,
知识点,,,,,指导变元,,,,,,,,
知识点,,,,,约束变元,,,,,,,,
知识点,,,,,一阶谓词,,,,,,,,
知识点,,,,,二阶谓词,,,,,,,,
分类,,,,谓词公式,,,,,,,,,
知识点,,,,,项,,,,,,,,
知识点,,,,,原子谓词公式,,,,,,,,
知识点,,,,,谓词公式,,,,,,,,
知识点,,,,,合取范式,,,,,,,,
知识点,,,,,析取范式,,,,,,,,
知识点,,,,,逻辑等价,,,,,,,,
知识点,,,,,逻辑蕴涵,,,,,,,,
分类,,,,,逻辑等价式,,,,,,,,
分类,,,,,逻辑蕴涵式,,,,,,,,
分类,,,,永真式与推理规则,,,,,,,,,
知识点,,,,,永真式,,,,,,,,
知识点,,,,,永假式,,,,,,,,
知识点,,,,,可满足式,,,,,,,,
分类,,,,自然语言命题的谓词形式表示,,,,,,,,,
分类,,,,基于谓词公式的形式演绎推理,,,,,,,,,
分类,,,归结演绎推理,,,,,,,,,,
分类,,,,子句与子句集,,,,,,,,,
分类,,,,命题逻辑中的归结原理,,,,,,,,,
分类,,,,替换与合一,,,,,,,,,
分类,,,,谓词逻辑中的归结原理,,,,,,,,,
分类,,,归结策略,,,,,,,,,,
分类,,,,问题的提出,,,,,,,,,
分类,,,,常用的归结策略,,,,,,,,,
分类,,,,归结策略的类型,,,,,,,,,
分类,,,归结反演程序举例,,,,,,,,,,
分类,学习与发现,,,,,,,,,,,,
分类,,机器学习,,,,,,,,,,,
知识点,,,机器学习,,,,,,统计学习,,,,
分类,,,机器学习概述,,,,,,,,,,
分类,,,,机器学习概念,,,,,,,,,
知识点,,,,,机器学习概念,,,,,,,概念性,
分类,,,,机器学习原理,,,,,,,,,
知识点,,,,,机器学习原理,,,,,,,,
分类,,,,机器学习分类,,,,,,,,,
知识点,,,,,机器学习分类,,,,,,,,
分类,,,,机器学习过程,,,,,统计学习,,,,
知识点,,,,,机器学习过程,,,,统计学习,,,程序性,
知识点,,统计学习,,,,,,机器学习;机器学习过程;机器学习过程,神经网络学习,,,,
分类,,神经网络学习,,,,,,统计学习,,,,,
分类,,,生物神经元,,,,,,,,,,
分类,,,,细胞体,,,,,,,,,
知识点,,,,,细胞核,,,,,,,,
知识点,,,,,细胞质,,,,,,,,
知识点,,,,,细胞膜,,,,,,,,
分类,,,,树突,,,,,,,,,
知识点,,,,,从细胞体向外延伸出许多突起，其中大部分突起呈树枝状,,,,,,,,
分类,,,,轴突,,,,,,,,,
知识点,,,,,由细胞体伸出的一条最长的突起，用来传出细胞体产生的输出信号,,,,,,,,
分类,,,,神经末梢,,,,,,,,,
知识点,,,,,轴突末端形成许多细的分枝,,,,,,,,
分类,,,,突触,,,,,,,,,
知识点,,,,,每一条神经末梢可以与其他神经元形成功能性接触,,,,,,,,
分类,,,人工神经元,,,,,,,,,,
分类,,,,结构模型,,,,,,,,,
知识点,,,,,一个多输入单输出的非线性阈值器件,,,,,,,,
知识点,,,,,输入信号量,,,,,,,,
知识点,,,,,输入权值,,,,,,,,
知识点,,,,,神经元输出,,,,,,,,
分类,,,,,激活函数,,,,,,,,
知识点,,,,,,阶跃函数,,,,,,,
知识点,,,,,,Sigmoid函数,,,,,,,
知识点,,,,,,分段线性函数,,,,,,,
知识点,,,,,,符号函数,,,,,,,
知识点,,,,,,双曲正切函数,,,,,,,
知识点,,,,,,ReLU函数,,,,,,,
分类,,,神经网络,,,,,,,,,,
分类,,,,定义,,,,,,,,,
知识点,,,,,将多个神经元按某种拓扑结构连接起来，就构成了神经网络,,,,,,,,
分类,,,,前向(也称前馈)网络,,,,,,,,,
分类,,,,,特征,,,,,,,,
知识点,,,,,,网络由若干层神经元组成，一般有输入层、中间层(又称隐藏层或隐层，可有一层或多层)和输出层，各层顺序连 接；且信息严格地按照从输入层进，经过中间层，从输出层出的方向流动。,,,,,,,
分类,,,,,输入层,,,,,,,,
知识点,,,,,,网络与外部环境的接口，它接受外部输入,,,,,,,
分类,,,,,中间层(又称隐藏层或隐层，可有一层或多层),,,,,,,,
知识点,,,,,,网络的内部处理  层，神经网络具有的模式变换能力，如模式分类、模式完善、特征抽取等，主要体现在隐层  神经元的处理能力上,,,,,,,
分类,,,,,输出层,,,,,,,,
知识点,,,,,,网络的输出接口，网络信息处理结果由输出层向外输出,,,,,,,
分类,,,,反馈前向网络,,,,,,,,,
分类,,,,,特征1,,,,,,,,
知识点,,,,,,它也是一种分层前向网络，但它的输出层到输入层具有反馈连接。,,,,,,,
知识点,,,,,输入层,,,,,,,,
知识点,,,,,中间层,,,,,,,,
知识点,,,,,输出层,,,,,,,,
分类,,,,,隐单元,,,,,,,,
知识点,,,,,,反馈的结果形成封闭环路具有反馈的单元被称为隐单元，其输出被称为内部输出。,,,,,,,
分类,,,,互联前向网络,,,,,,,,,
分类,,,,,特征2,,,,,,,,
知识点,,,,,,它也是一种分层前向网络，但它的同层神经元之间有相互连接。同一层内单元的相互连接使它们之间有彼此牵制作用。,,,,,,,
知识点,,,,,输入层1,,,,,,,,
知识点,,,,,中间层1,,,,,,,,
知识点,,,,,输出层1,,,,,,,,
分类,,,,广泛互联网络,,,,,,,,,
分类,,,,,特征3,,,,,,,,
知识点,,,,,,广泛互联是指网络中任意两个神经元之间都可以或可能是可达的，即存在连接路径)。著名的Hopfield 网络、玻尔兹曼机模型结构就属此类。,,,,,,,
知识点,,,,,输入层2,,,,,,,,
知识点,,,,,中间层2,,,,,,,,
知识点,,,,,输出层2,,,,,,,,
分类,,,学习机理,,,,,,,,,,
分类,,,,学习规则,,,,,,,,,
知识点,,,,,相关规则,,,,,,,,
知识点,,,,,误差修正规则,,,,,,,,
分类,,,,学习方法,,,,,,,,,
分类,,,,,数据特点分类,,,,,,,,
知识点,,,,,,有监督学习,,,,,,,
知识点,,,,,,无监督学习,,,,,,,
分类,,,,,内部状态分类,,,,,,,,
知识点,,,,,,权值修正,,,,,,,
知识点,,,,,,拓扑变化,,,,,,,
知识点,,,,,,权值与拓扑修正,,,,,,,
分类,,,,,学习分类,,,,,,,,
知识点,,,,,,确定性学习,,,,,,,
知识点,,,,,,随机性学习,,,,,,,
分类,,,神经网络模型,,,,,,,,,,
分类,,,,按网络结构分类,,,,,,,,,
知识点,,,,,分层结构,,,,,,,,
知识点,,,,,互联结构,,,,,,,,
知识点,,,,,反馈网络,,,,,,,,
分类,,,,按学习方式分类,,,,,,,,,
知识点,,,,,监督(导师)学习网络,,,,,,,,
知识点,,,,,无监督(导 师)学习网络,,,,,,,,
分类,,,,按网络的状态分类,,,,,,,,,
知识点,,,,,连续时间变化状态,,,,,,,,
知识点,,,,,离散时 间变化状态,,,,,,,,
分类,,,,按网络的活动方式分类,,,,,,,,,
知识点,,,,,确定性网络,,,,,,,,
知识点,,,,,随机性网络,,,,,,,,
分类,,,感知器,,,,,,,,,,
分类,,,,定义1,,,,,,,,,
知识点,,,,,仅有一个神经元的最简单的神经网络，由 Frank   Rosenblatt 于1957年提出,,,,,,,,
分类,,,,激活函数1,,,,,,,,,
知识点,,,,,阶跃函数1,,,,,,,,
知识点,,,,,符号函数1,,,,,,,,
分类,,,BP 网络,,,,,,,,,,
知识点,,,,误差反向传播网络是应用最广泛的一种神经网络模型。,,,,,,,,,
分类,,,,拓扑结构,,,,,,,,,
知识点,,,,,分层前向网络,,,,,,,,
分类,,,,神经元的特性函数,,,,,,,,,
知识点,,,,,Sigmoid 函数,,,,,,,,
分类,,,,输入,,,,,,,,,
知识点,,,,,连续信号量(实数),,,,,,,,
分类,,,,学习方式,,,,,,,,,
知识点,,,,,有监督学习1,,,,,,,,
分类,,,,学习算法,,,,,,,,,
知识点,,,,,误差反向传播算法，简称 BP 学习算法,,,,,,,,
分类,,,深度学习,,,,,,,,,,
分类,,,,定义2,,,,,,,,,
知识点,,,,,基于深度神经网络的神经网络学习或者说机器学习,,,,,,,,
知识点,,,,深度置信网络,,,,,,,,,
知识点,,,,卷积神经网络,,,,,,,,,
知识点,,,,循环神经网络,,,,,,,,,
知识点,,,,递归神经网络,,,,,,,,,
知识点,,,,长短期记忆网络,,,,,,,,,
知识点,,,,生成对抗网络,,,,,,,,,
分类,,,,深度学习框架与平台,,,,,,,,,
知识点,,,,,TensorFlow,,,,,,,,
知识点,,,,,Caffe,,,,,,,,
知识点,,,,,Theano,,,,,,,,
知识点,,,,,Torch/PyTorch,,,,,,,,
知识点,,,,,Deeplearing4j,,,,,,,,
知识点,,,,,PaddlePaddle,,,,,,,,
分类,,数据挖掘与知识发现,,,,,,,,,,,
分类,,,数据挖掘,,,,,,,,,,
分类,,,,定义3,,,,,,,,,
知识点,,,,,数据开采、数据采掘等，意思是从数据中提取或挖掘知识,,,,,,,,
分类,,,,过程,,,,,,,,,
分类,,,,,数据准备,,,,,,,,
知识点,,,,,,数据选取,,,,,,,
知识点,,,,,,数据预处理,,,,,,,
知识点,,,,,,数据变换,,,,,,,
知识点,,,,,数据开采,,,,,,,,
知识点,,,,,结果的解释评估,,,,,,,,
分类,,,,对象,,,,,,,,,
知识点,,,,,数据库,,,,,,,,
知识点,,,,,数据仓库,,,,,,,,
知识点,,,,,Web 信息,,,,,,,,
知识点,,,,,图像和视频数据,,,,,,,,
分类,,,,任务,,,,,,,,,
知识点,,,,,数据总结,,,,,,,,
知识点,,,,,概念描述,,,,,,,,
知识点,,,,,分类,,,,,,,,
知识点,,,,,聚类,,,,,,,,
知识点,,,,,相关性分析,,,,,,,,
知识点,,,,,偏差分析,,,,,,,,
知识点,,,,,建模,,,,,,,,
分类,,,,方法,,,,,,,,,
知识点,,,,,统计方法,,,,,,,,
知识点,,,,,机器学习方法,,,,,,,,
知识点,,,,,粗糙集,,,,,,,,
知识点,,,,,智能计算方法,,,,,,,,
知识点,,,,,可视化,,,,,,,,
知识点,,,,工具,,,,,,,,,
知识点,,,知识发现,,,,,,,,,,
分类,,,关联规则,,,,,,,,,,
分类,,,,定义4,,,,,,,,,
知识点,,,,,表示事件或者数据项之间有某种相关性的一 种产生式规则。,,,,,,,,
分类,,,,度量,,,,,,,,,
分类,,,,,支持度(support),,,,,,,,
知识点,,,,,,规则前、后件同时出现的概率,,,,,,,
分类,,,,,置信度(confidence),,,,,,,,
知识点,,,,,,规则前件出现时后件也出现的概率,,,,,,,
知识点,,,,,期望可信度(expectedconfidence),,,,,,,,
知识点,,,,,作用度(lift),,,,,,,,
分类,,,,发现机理,,,,,,,,,
知识点,,,,,当其中的一(几)个再次出现或发生时，人们往往就会预测另一(几)个也会出现或发生。,,,,,,,,
分类,,,,基本方法,,,,,,,,,
知识点,,,,,第一步，计算交易集中的所有交易的支持度，取出其中的所有频繁交易和频繁子交易。,,,,,,,,
知识点,,,,,"第二步，对所得的各个频繁交易(包括子交易),按上述方法构造所有的候选关联规则。",,,,,,,,
知识点,,,,,第三步，计算各候选规则的支持度和置信度，保留二者都大于或等于所设阈值的规则作为所发现的关联规则。,,,,,,,,
分类,,,,强规则,,,,,,,,,
知识点,,,,,如果一个关联规则的支持度和置信度都大于所设的阈值，则称该关联规则为强规则。,,,,,,,,
分类,,,,Apriori算法,,,,,,,,,
知识点,,,,,"从交易集的每一个交易中拆分出所有1-交易(k-交易是含有 k 个物品的交易), 将它们组成集合C₁ (当然相同的1-交易在C₁ 中只有一个),然后计算C₁ 中每个交易的支持度，提取其中满足支持度阈值条件的频繁交易组成集合L₁。",,,,,,,,
知识点,,,,,"令C₂=L₁⊗L₁, 计算C₂ 中每个交易的支持度，然后提取其中满足支持度阈值条件的频繁交易组成集合L₂。",,,,,,,,
知识点,,,,,"令C₃=L₂⊗L₂ ,     计算C₃ 中每个交易的支持度，然后提取其中满足支持度阈值条件的频繁交易组成集合L₃。",,,,,,,,
知识点,,,,,一直这样重复做下去，直到某个Lk+1=空,,,,,,,,
分类,,,,类型,,,,,,,,,
分类,,,,,根据规则中所描述的值的类型,,,,,,,,
知识点,,,,,,布尔关联规则,,,,,,,
知识点,,,,,,量化关联规则,,,,,,,
分类,,,,,根据规则中涉及数据的维数,,,,,,,,
知识点,,,,,,单维关联规则,,,,,,,
知识点,,,,,,多维关联规则,,,,,,,
分类,,,,,根据规则所涉及的抽象层次,,,,,,,,
知识点,,,,,,单层关联规则组成的规则集,,,,,,,
知识点,,,,,,多层关联规则组成的规则集,,,,,,,
分类,,,,,根据关联规则中的项或属性是否软语言值,,,,,,,,
知识点,,,,,,软语言关联规则,,,,,,,
知识点,,,,,,硬语言关联规则,,,,,,,
分类,,,k-均值聚类算法,,,,,,,,,,
分类,,,,聚类,,,,,,,,,
分类,,,,,定义5,,,,,,,,
知识点,,,,,,将一个数据集按数据点间的相似关系划分为若干互不相交的子集，这样的子集称为簇(cluster) 。 数据点的相似性用某种距离或近似度度量。,,,,,,,
分类,,,,,步骤,,,,,,,,
知识点,,,,,,"对于数据集S,随机指定或创建k个数据点(向量)μ1,H₂,…,μp ,分别作为k个簇的 质心(即聚类中心)。",,,,,,,
知识点,,,,,,"计算S中每个数据点x到每个质心μ的距离，然后将 x划归给距离最近的质心μ₁所代表的簇C₁(这时得到k个簇C₁,C₂,…,C, 构成S的一个划分(C₁,C₂,…,C,})。",,,,,,,
知识点,,,,,,"计算每个簇C中所有点x的均值(向量),并将该向量作为簇C,的新质心",,,,,,,
知识点,,,,,,"重复步骤(2)和步骤(3),直到每个点x的所属簇不再发生改变，也就是质心μ;(j=1,2,…,k) 不再改变，这时所得的簇集{C₁,C₂,…,Ca}就是所求的数据集S的一个最佳划分。",,,,,,,
分类,,,目的,,,,,,,,,,
知识点,,,,从数据集中抽取和精化一般规律或模式。,,,,,,,,,
分类,,,分布式并行计算模型和框架,,,,,,,,,,
分类,,,,分布式并行计算类型,,,,,,,,,
知识点,,,,,模型并行,,,,,,,,
知识点,,,,,数据并行,,,,,,,,
知识点,,,,,混合并行,,,,,,,,
分类,,,,分布式并行计算框架,,,,,,,,,
分类,,,,,Hadoop,,,,,,,,
知识点,,,,,,HDFS,,,,,,,
知识点,,,,,,MapReduce,,,,,,,
分类,感知与响应,,,,,,,,,,,,
分类,,模式识别,,,,,,,,,,,
分类,,,模式,,,,,,,,,,
知识点,,,,能够表征或刻画被识别对象类属特征的信息模型称为对象的模式,,,,,,,,,
分类,,,模式类,,,,,,,,,,
知识点,,,,具有某些共同特性的模式的集合称为模式类,,,,,,,,,
分类,,,模式识别1,,,,,,,,,,
知识点,,,,判定一个待识模式的类属的过程称为模式识别,,,,,,,,,
分类,,,模式的表示,,,,,,,,,,
分类,,,,向量,,,,,,,,,
知识点,,,,,"用向量表示对象模式就是以对象的诸特征值作为分量而组成的一个n 维向量X, 即 X=(x₁,x₂,…,xn)∈R”,            其 中x;(i=1,2,…,n)          为相应对象的第i 个特征值。",,,,,,,,
分类,,,,,特征向量,,,,,,,,
知识点,,,,,,表示对象模式的 n 维向量,,,,,,,
分类,,,,,特征空间,,,,,,,,
知识点,,,,,,相应的向量空间 R*CR” 称为特征空间,,,,,,,
分类,,,,字符串,,,,,,,,,
知识点,,,,,字符串反映的是对象的构造特征，或者说是用形状来描述对象的，因此字符串是被识对象的结构模式。,,,,,,,,
分类,,,工作原理,,,,,,,,,,
分类,,,,信息获取,,,,,,,,,
知识点,,,,,采集被识别对象的原始信息。,,,,,,,,
分类,,,,预处理,,,,,,,,,
知识点,,,,,除去噪声和干扰信息。,,,,,,,,
分类,,,,特征选取,,,,,,,,,
知识点,,,,,根据具体的识别目的和任务对已知的对象特征进行选择。,,,,,,,,
分类,,,,基元选取,,,,,,,,,
知识点,,,,,分析、选择被识对象的基本构造元素,,,,,,,,
分类,,,模式识别方法分类,,,,,,,,,,
分类,,,,依据模式的表示形式,,,,,,,,,
分类,,,,,基于特征向量的模式识别,,,,,,,,
知识点,,,,,,统计模式识别,,,,,,,
知识点,,,,,,统计模式识别主要是依据统计、概率理论(主要是贝叶斯决策理论)、统计学习理论及其他数学理论和方法通过机器学习进行建模，然后进行分类决策的。,,,,,,,
分类,,,,,基于字符串的模式识别,,,,,,,,
知识点,,,,,,结构模式识别处理的是字符串或树结构的模式，模式类描述为形式语言的文法，识别器(即分类器)就是有限状态自动机。,,,,,,,
知识点,,,,,,结构模式识别,,,,,,,
分类,,,统计模式识别,,,,,,,,,,
分类,,,,距离分类法,,,,,,,,,
知识点,,,,,标准模式法,,,,,,,,
知识点,,,,,平均距离法,,,,,,,,
知识点,,,,,最近邻法,,,,,,,,
知识点,,,,几何分类法,,,,,,,,,
分类,,,,概率分类法,,,,,,,,,
知识点,,,,,基于最小错误率的贝叶斯决策,,,,,,,,
知识点,,,,,基于最小风险的贝叶斯决策,,,,,,,,
知识点,,,朴素贝叶斯分类算法,,,,,,,,,,
知识点,,,概率密度函数估计,,,,,,,,,,
分类,,数-语互换,,,,,,,,,,,
分类,,,数-语转换,,,,,,,,,,
知识点,,,,数量值到纯软语言值的转换,,,,,,,,,
知识点,,,,数量值到程度化软语言值的转换,,,,,,,,,
分类,,,语-数转换,,,,,,,,,,
知识点,,,,纯软语言值到数量值的转换,,,,,,,,,
知识点,,,,程度化软语言值到数量值的转换,,,,,,,,,
分类,,,带数-语互换接口的推理系统,,,,,,,,,,
知识点,,,,将数量值转换为软语言值，然后用其进行推理,,,,,,,,,
知识点,,,,将某一推理结果(软语言值)作为软决策，然后转换为数量值用于执行,,,,,,,,,
知识点,,,,将数量值转换为软语言值，然后用其进行推理；将推理结果(软语言值)作为软 决策，然后转换为数量值用于执行,,,,,,,,,
知识点,,自然语言处理,,,,,,,,,,,
分类,系统与建造,,,,,,,,,,,,
分类,,专家(知识)系统,,,,,,,,,,,
分类,,,定义17,,,,,,,,,,
知识点,,,,专家系统就是能像人类专家一样解决困难、复杂的实际问题的计算机(软件)系统。,,,,,,,,,
分类,,,要素,,,,,,,,,,
知识点,,,,应用于某专门领域。,,,,,,,,,
知识点,,,,拥有专家级知识。,,,,,,,,,
知识点,,,,能模拟专家的思维。,,,,,,,,,
知识点,,,,能达到专家级水平。,,,,,,,,,
分类,,,特点2,,,,,,,,,,
知识点,,,,从处理的问题性质看，专家系统善于解决那些不确定性的、非结构化的、没有算法解或虽有算法解但在现有的机器上无法实施的困难问题。,,,,,,,,,
知识点,,,,从处理问题的方法看，专家系统则是靠知识和推理来解决问题，而不像传统软件系统使用固定的算法来解决问题。,,,,,,,,,
知识点,,,,从系统的结构来看，专家系统一般强调知识与推理的分离，因而系统具有很好的灵活性和可扩充性。,,,,,,,,,
知识点,,,,专家系统一般还具有解释功能。即在运行过程中一方面能回答用户提出的问题，另一方面还能对最后的输出(结论)或处理问题的过程作出解释。,,,,,,,,,
知识点,,,,具有“自学习”能力，即不断对自己的知识进行扩充、完善和提炼。,,,,,,,,,
知识点,,,,专家系统不像人类专家那样容易疲劳、遗忘，易受环境、情绪等的影响，它可始终如一地以专家级的高水平求解问题。,,,,,,,,,
分类,,,专家系统的类型,,,,,,,,,,
分类,,,,按用途划分,,,,,,,,,
知识点,,,,,诊断型,,,,,,,,
知识点,,,,,解释型,,,,,,,,
知识点,,,,,预测型,,,,,,,,
知识点,,,,,决策型,,,,,,,,
知识点,,,,,设计型,,,,,,,,
知识点,,,,,规划型,,,,,,,,
知识点,,,,,控制型,,,,,,,,
知识点,,,,,调度型,,,,,,,,
分类,,,,按输出结果划分,,,,,,,,,
知识点,,,,,分析型,,,,,,,,
知识点,,,,,设计型1,,,,,,,,
分类,,,,按知识表示划分,,,,,,,,,
知识点,,,,,基于产生式规则的专家系统,,,,,,,,
知识点,,,,,基于一阶谓词的专家系统,,,,,,,,
知识点,,,,,基于框架的专家系统,,,,,,,,
知识点,,,,,基于语义网的专家系统,,,,,,,,
分类,,,,按结构划分,,,,,,,,,
知识点,,,,,集中式,,,,,,,,
知识点,,,,,分布式,,,,,,,,
分类,,,,按采用的技术划分,,,,,,,,,
知识点,,,,,符号推理专家系统,,,,,,,,
知识点,,,,,神经网络专家系统,,,,,,,,
分类,,,,按规模划分,,,,,,,,,
知识点,,,,,大型协同式专家系统,,,,,,,,
知识点,,,,,微专家系统,,,,,,,,
分类,,,系统结构,,,,,,,,,,
知识点,,,,知识库,,,,,,,,,
知识点,,,,推理机,,,,,,,,,
知识点,,,,动态数据库,,,,,,,,,
知识点,,,,人机界面,,,,,,,,,
知识点,,,,解释模块,,,,,,,,,
知识点,,,,知识库管理系统,,,,,,,,,
分类,,,黑板模型,,,,,,,,,,
知识点,,,,黑板,,,,,,,,,
知识点,,,,知识源,,,,,,,,,
知识点,,,,控制机构,,,,,,,,,
分类,,Agent系统,,,,,,,,,,,
分类,,,Agent,,,,,,,,,,
分类,,,,定义16,,,,,,,,,
知识点,,,,,一种具有智能的实体,,,,,,,,
分类,,,,基本特性,,,,,,,,,
知识点,,,,,自主性,,,,,,,,
知识点,,,,,反应性,,,,,,,,
知识点,,,,,适应性,,,,,,,,
知识点,,,,,社会性,,,,,,,,
分类,,,,类型1,,,,,,,,,
分类,,,,,基于理论模型,,,,,,,,
知识点,,,,,,反应型,,,,,,,
知识点,,,,,,思考型(或认知型),,,,,,,
知识点,,,,,,复合型,,,,,,,
分类,,,,,基于特性模型,,,,,,,,
知识点,,,,,,反应式Agent,,,,,,,
知识点,,,,,,BDI型 Agent,,,,,,,
知识点,,,,,,社会Agent,,,,,,,
知识点,,,,,,演化Agent,,,,,,,
知识点,,,,,,人格化Agent,,,,,,,
分类,,,,,所承担的工作和任务性质,,,,,,,,
知识点,,,,,,信息型Agent、合作型Agent、 接口型Agent、移动型Agent,,,,,,,
知识点,,,,,,专用Agent 和通用Agent,,,,,,,
分类,,,Agent的结构,,,,,,,,,,
知识点,,,,思考型Agent结构模型,,,,,,,,,
知识点,,,,简化Agent 结构模型,,,,,,,,,
知识点,,,Web Agent,,,,,,,,,,
分类,,,多Agent系统,,,,,,,,,,
分类,,,,特征4,,,,,,,,,
知识点,,,,,每个Agent 拥有解决问题的不完全的信息或能力。,,,,,,,,
知识点,,,,,没有系统全局控制。,,,,,,,,
知识点,,,,,数据是分散的。,,,,,,,,
知识点,,,,,计算是异步的。,,,,,,,,
分类,,,,基本规范,,,,,,,,,
知识点,,,,,多Agent 系统的体系结构。,,,,,,,,
知识点,,,,,多Agent 系统中Agent心智状态包括与交互有关的心智状态的选择与描述。,,,,,,,,
知识点,,,,,多Agent 系统的特性、这些特性之间的关系以及如何描述这些特性及其关系。,,,,,,,,
知识点,,,,,多Agent 系统中Agent 之间的交互和推理。,,,,,,,,
知识点,,,,,多Agent之间的合作与竞争。,,,,,,,,
知识点,,,,,多Agent 的学习和适应。,,,,,,,,
分类,,,,体系结构,,,,,,,,,
知识点,,,,,Agent 网络,,,,,,,,
知识点,,,,,Agent联盟,,,,,,,,
知识点,,,,,黑板结构,,,,,,,,
分类,,智能机器人,,,,,,,,,,,
分类,,,阶段,,,,,,,,,,
知识点,,,,"第一阶段的机器人只有“手”,以固定程序工作，不具有外界信息的反馈能力；",,,,,,,,,
知识点,,,,第二阶段的机器人具有对外界信息的反馈能力，即有了感觉，如力觉、触觉、视觉等；,,,,,,,,,
知识点,,,,第三阶段，即所谓“智能机器人”阶段，这一阶段的机器人已经具有了自主性，有自行学习、推理、决策、规划等能力。,,,,,,,,,
分类,,,具备4种机能,,,,,,,,,,
知识点,,,,感知机能——获取外部环境信息以便进行自我行动监视的机能：,,,,,,,,,
知识点,,,,运动机能——施加于外部环境的相当于人的手、脚的动作机能；,,,,,,,,,
知识点,,,,思维机能——求解问题的推理、判断机能；,,,,,,,,,
知识点,,,,人-机通信机能——理解指示命令、输出内部状态、与人进行信息交流的机能。,,,,,,,,,
知识点,,,机器人感知,,,,,,,,,,
分类,,,机器人规划,,,,,,,,,,
分类,,,,基本任务,,,,,,,,,
知识点,,,,,在一个特定的工作区域中自动地生成从初始状态到目标状态的动作序列、运动路径和轨迹的控制程序。,,,,,,,,
分类,,,,规划系统分为两级,,,,,,,,,
分类,,,,,任务规划子系统,,,,,,,,
知识点,,,,,,任务规划子系统根据任务命令，自动生成相应的机器人执行程序,,,,,,,
分类,,,,,运动规划子系统,,,,,,,,
知识点,,,,,,运动规划子系统首先将任务规划的结果变成一个无碰撞的操作器运动路径，这一步称为路径规划；然后再将路径变为操作器各关节的空间坐标，形成运动轨迹，这一步称为轨迹规划。,,,,,,,
分类,,,,基本技术,,,,,,,,,
知识点,,,,,问题或状态的表示,,,,,,,,
知识点,,,,,搜索策略,,,,,,,,
知识点,,,,,子目标冲突问题,,,,,,,,
分类,,,机器人控制,,,,,,,,,,
分类,,,,位置控制,,,,,,,,,
分类,,,,,定义14,,,,,,,,
知识点,,,,,,"对于路径规划给出的运动轨迹(即路径),控制机器人的肢体(如机械手)产生相应的动作。",,,,,,,
分类,,,,力控制,,,,,,,,,
分类,,,,,定义15,,,,,,,,
知识点,,,,,,对机器人的肢体所发出的作用力(如机械手的握力和推力)大小的控制。,,,,,,,
分类,,,,控制结构,,,,,,,,,
知识点,,,,,第一层负责任务规划，把目标任务分解为初级任务序列。,,,,,,,,
知识点,,,,,第二层负责路径规划，把初级移动命令分解为一系列字符串，这些字符串定义了一条可避免碰撞和死点的运动路径。,,,,,,,,
知识点,,,,,第三层的基本功能是计算惯量动力学并产生平滑轨迹，在基本坐标系中控制末端执行器。,,,,,,,,
知识点,,,,,第四层为伺服和坐标变换，完成从基本坐标到关节坐标系的坐标变换以及关节位置、速度和力的伺服控制。,,,,,,,,
分类,,,机器人系统的软件结构,,,,,,,,,,
知识点,,,,反应层,,,,,,,,,
知识点,,,,执行层,,,,,,,,,
知识点,,,,思考层,,,,,,,,,
分类,,,机器人程序设计与语言,,,,,,,,,,
分类,,,,机器人程序设计,,,,,,,,,
知识点,,,,,直接示教方式,,,,,,,,
知识点,,,,,离线数据程序设计方式,,,,,,,,
知识点,,,,,机器人语言方式,,,,,,,,
分类,,,,机器人程序设计语言,,,,,,,,,
知识点,,,,,机器人语言通常分为3级：动作水平级、对象物水平级和作业目标水平级(也称任务级)。,,,,,,,,
知识点,,,,,通用机器人语言GRL,,,,,,,,
知识点,,,,,反应式行动规划系统 RAPS,,,,,,,,
知识点,,,,,GOLOG  语言,,,,,,,,
知识点,,,,,嵌入式系统C++ 语言CES,,,,,,,,
知识点,,,,,ALISP,,,,,,,,
分类,,智能计算机与智能化网络,,,,,,,,,,,
分类,,,智能计算机,,,,,,,,,,
分类,,,,智能硬件平台,,,,,,,,,
分类,,,,,定义7,,,,,,,,
知识点,,,,,,直接支持智能系统开发和运行的智能硬件设备。,,,,,,,
分类,,,,,LISP机,,,,,,,,
知识点,,,,,,LISP机是一种面向符号处理、直接以LISP语言为机器语言的计算机，由美国麻省 理工学院AI实验室的R.格林布拉特于20世纪70年代初首先研制成功。,,,,,,,
分类,,,,,PROLOG机,,,,,,,,
知识点,,,,,,是一种面向逻辑推理、直接以PROLOG语言为机器语言的计算机。,,,,,,,
分类,,,,,人工智能芯片,,,,,,,,
知识点,,,,,,GPU,,,,,,,
知识点,,,,,,FPGA,,,,,,,
知识点,,,,,,TPU,,,,,,,
知识点,,,,,类脑芯片,,,,,,,,
分类,,,,智能操作系统,,,,,,,,,
分类,,,,,定义8,,,,,,,,
知识点,,,,,,以智能计算机硬件为基础，能实现计算机硬软件资源的智能管理与调度，具有智能接口，并能支撑外层的智能应用程序的新一代操作系统。,,,,,,,
分类,,,,,特点,,,,,,,,
知识点,,,,,,并行性、分布性和智能性,,,,,,,
分类,,,智能化网络,,,,,,,,,,
分类,,,,智能网,,,,,,,,,
分类,,,,,定义9,,,,,,,,
知识点,,,,,,在原有通信网络(即交换与传输网 络结构)基础上，为快速、方便、经济、灵活地提供电信新业务(也称增值业务)而设置的一 种附加网络结构。,,,,,,,
分类,,,,,结构,,,,,,,,
分类,,,,,,业务交换点,,,,,,,
知识点,,,,,,,智能网中的交换机，但它只用来完成基本的呼叫处理和接续控制以及与SCP 的信息交互,,,,,,
分类,,,,,,业务控制点,,,,,,,
知识点,,,,,,,一般由大中型计算机和大型高速实时数据库构成，用来存放智能业务程序和数据,,,,,,
知识点,,,,,,业务数据点,,,,,,,
知识点,,,,,,业务管理点,,,,,,,
知识点,,,,,,业务生成环境,,,,,,,
分类,,,,,特点1,,,,,,,,
知识点,,,,,,功能分离，集中控制,,,,,,,
知识点,,,,,,模块编程，组合配置,,,,,,,
分类,,,,智能Web,,,,,,,,,
分类,,,,,万维网,,,,,,,,
分类,,,,,,定义10,,,,,,,
知识点,,,,,,,"WWW(World  Wide  Web,万维网),简称 Web, 是基于Internet  的一个超大型信息网 络系统，于1991年由Tim   Berners-Lee创建。",,,,,,
分类,,,,,语义Web,,,,,,,,
分类,,,,,,定义11,,,,,,,
知识点,,,,,,,语义Web则是进一步将(文档中的)知识(数据)互联，或者说将文档变成机器可读的知识结构(元数据)而形成的一种数据Web。,,,,,,
分类,,,,,,语义Web的体系结构,,,,,,,
知识点,,,,,,,第1层是基础层，主要包括Unicode和 URI。,,,,,,
知识点,,,,,,,第2层是句法层，包括XML、NS和xml schema 。,,,,,,
知识点,,,,,,,第3层是数据层，包括RDF 和 rdfschema,,,,,,
知识点,,,,,,,"第4层是语义层，Ontology   vocabulary,即本体词汇。",,,,,,
知识点,,,,,,,第5层是逻辑层，在前面各层的基础上进行逻辑推理操作；,,,,,,
知识点,,,,,,,第6层是验证层，根据逻辑陈述进行验证，以证明输出的可靠性以及其是否符合用户的要求；,,,,,,
知识点,,,,,,,第7层是信任层，是语义Web安全的组成部分，主要负责发布语义Web 所能支持的信任评估。,,,,,,
分类,,,,,,语义Web的关键技术,,,,,,,
知识点,,,,,,,XML,,,,,,
知识点,,,,,,,RDF,,,,,,
知识点,,,,,,,Ontology,,,,,,
分类,,,,网络的智能化管理与控制,,,,,,,,,
分类,,,,,在管理功能中引入智能技术,,,,,,,,
知识点,,,,,,在配置管理中，可以用人工智能中自动规划与配置技术实现优化配置，提高网络资源利用率。,,,,,,,
知识点,,,,,,在故障管理中，可建立故障诊断专家系统。,,,,,,,
知识点,,,,,,在性能管理中，性能分析可以采用知识库和专家系统技术提高其水平和速度。,,,,,,,
知识点,,,,,,在安全管理中，入侵检测中可使用多种智能技术,,,,,,,
分类,,,,,在管理方法中引入智能技术,,,,,,,,
知识点,,,,,,"智能Agent具有一定的知识，它能自治地检测环境(被管对象及其自身的状态),经过分析、推理后，对环境进行调整和改造，必要时还可与其他智能 Agent 通信联络。",,,,,,,
知识点,,,,,,专家系统技术,,,,,,,
分类,,,,,智能网络管理系统,,,,,,,,
知识点,,,,,,处理不确定性问题的能力,,,,,,,
知识点,,,,,,协作能力,,,,,,,
知识点,,,,,,应变能力,,,,,,,
知识点,,,,,,解释和推理能力,,,,,,,
知识点,,,,,,记忆和学习能力,,,,,,,
分类,,,,智能化检索,,,,,,,,,
分类,,,,,搜索引擎,,,,,,,,
分类,,,,,,定义12,,,,,,,
知识点,,,,,,,专门为用户提供信息发布和信息查询服务的一种软件系统。,,,,,,
知识点,,,,,,信息搜集：搜集网上所发布的各种信息，并录入其数据库。,,,,,,,
知识点,,,,,,信息组织：对搜集来的信息进行分类、索引和摘要。,,,,,,,
知识点,,,,,,信息检索：提供多种检索方式，为用户从数据库中检索出所需求的信息，满足信息需求者的需要。,,,,,,,
分类,,,,,智能搜索引擎,,,,,,,,
知识点,,,,,,信息分类技术,,,,,,,
知识点,,,,,,基于内容的自动索引技术,,,,,,,
分类,,,,,,理解式自动文摘技术,,,,,,,
知识点,,,,,,,频度统计法,,,,,,
知识点,,,,,,,关键位置判定法,,,,,,
知识点,,,,,,,句法频度结合法,,,,,,
知识点,,,,,,基于Agent 的网上信息搜索,,,,,,,
知识点,,,,,,基于知识图谱的语义搜索,,,,,,,
知识点,,,,,,网页排序技术,,,,,,,
分类,,,,推荐系统,,,,,,,,,
分类,,,,,定义13,,,,,,,,
知识点,,,,,,推荐系统就是专门为上网用户适时推荐或推送有关资讯、物品或服务等的一种个性化信息服务系统。,,,,,,,
分类,,,,,组成,,,,,,,,
知识点,,,,,,推荐系统一般由用户资料、物品资料、分析建模、用户模型及物品模型、推荐算法等模块组成,,,,,,,
分类,,,,,推荐算法,,,,,,,,
知识点,,,,,,推荐算法把用户模型中的用户的兴趣、偏好、 相似性等信息和知识与物品模型中的特征、分类、关联等信息和知识相匹配，并进行相应 的推理、计算和筛选，找到用户可能感兴趣的物品，然后推荐给用户。,,,,,,,
知识点,,,,,,协同过滤推荐,,,,,,,
知识点,,,,,,基于内容的推荐,,,,,,,
知识点,,,,,,基于关联规则的推荐,,,,,,,
知识点,,,,,,基于效用 的推荐,,,,,,,
知识点,,,,,,基于知识的推荐,,,,,,,
知识点,,,,,,基于标签的推荐,,,,,,,
知识点,,,,,,混合推荐,,,,,,,
分类,概述与工具,,,,,,,,,,,,
分类,,人工智能的概念,,,,,,,,,,,
分类,,,人工智能,,,,,,,,,,
分类,,,,定义6,,,,,,,,,
知识点,,,,,"人工智能是那些与人的思维相关的活动，诸如决策、问题求解和学习等的自动化 (Bellman,1978)。",,,,,,,,
知识点,,,,,"人工智能是一种计算机能够思维，使机器具有智力的激动人心的新尝试(Haugeland,1985)",,,,,,,,
知识点,,,,,"人工智能是研究如何让计算机做现阶段只有人才能做得好的事情(Rich Knight,1991)",,,,,,,,
知识点,,,,,"人工智能是那些使知觉、推理和行为成为可能的计算的研究(Winston,1992)。",,,,,,,,
知识点,,,,,"广义地讲，人工智能是关于人造物的智能行为，而智能行为包括知觉、推理、学习、交流和在复杂环境中的行为(Nilsson,1998)。",,,,,,,,
知识点,,,,,Stuart Russell和Peter Norvig 则把已有的一些人工智能定义分为4类：像人一样思考的系统、像人一样行动的系统、理性地思考的系统、理性地行动的系统(2003)。,,,,,,,,
知识点,,,,图灵测试,,,,,,,,,
知识点,,,,中文屋子,,,,,,,,,
知识点,,,,脑智能,,,,,,,,,
知识点,,,,群智能,,,,,,,,,
知识点,,,,弱人工智能,,,,,,,,,
知识点,,,,强人工智能,,,,,,,,,
知识点,,,,符号智能,,,,,,,,,
知识点,,,,计算智能,,,,,,,,,
知识点,,,,统计智能,,,,,,,,,
知识点,,,,交互智能,,,,,,,,,
分类,,,人工智能应用,,,,,,,,,,
知识点,,,,难题求解,,,,,,,,,
知识点,,,,自动规划,,,,,,,,,
知识点,,,,机器博弈,,,,,,,,,
知识点,,,,机器翻译与写作,,,,,,,,,
知识点,,,,定理证明,,,,,,,,,
知识点,,,,自动程序设计,,,,,,,,,
知识点,,,,智能控制,,,,,,,,,
知识点,,,,智能管理,,,,,,,,,
知识点,,,,智能决策,,,,,,,,,
知识点,,,,智能通信,,,,,,,,,
知识点,,,,智能预测,,,,,,,,,
知识点,,,,智能仿真,,,,,,,,,
知识点,,,,智能设计与制造,,,,,,,,,
知识点,,,,智能车辆与智能交通,,,,,,,,,
知识点,,,,智能诊断与治疗,,,,,,,,,
知识点,,,,智能生物信息处理,,,,,,,,,
知识点,,,,智能教育,,,,,,,,,
知识点,,,,智能人-机接口,,,,,,,,,
知识点,,,,模式识别,,,,,,,,,
知识点,,,,智能机器人,,,,,,,,,
知识点,,,,数据挖掘与知识发现,,,,,,,,,
知识点,,,,计算机辅助创新,,,,,,,,,
知识点,,,,计算机文艺创作,,,,,,,,,
分类,,,研究内容,,,,,,,,,,
知识点,,,,搜索与求解,,,,,,,,,
知识点,,,,知识与推理,,,,,,,,,
知识点,,,,学习与发现,,,,,,,,,
知识点,,,,发明与创造,,,,,,,,,
知识点,,,,感知与响应,,,,,,,,,
知识点,,,,理解与交流,,,,,,,,,
知识点,,,,记忆与联想,,,,,,,,,
知识点,,,,竞争与协作,,,,,,,,,
知识点,,,,系统与建造,,,,,,,,,
知识点,,,,应用与工程,,,,,,,,,
知识点,,,研究途径与方法,,,,,,,,,,
分类,,人工智能程序设计语言,,,,,,,,,,,
知识点,,,函数型语言,,,,,,,,,,
知识点,,,逻辑型语言,,,,,,,,,,
知识点,,,面向对象语言,,,,,,,,,,
知识点,,,计算型语言,,,,,,,,,,
知识点,,,混合型语言,,,,,,,,,,
分类,搜索与求解,,,,,,,,,,,,
知识点,,图搜索与问题求解,,,,,,,,,,,
分类,,基于遗传算法的随机优化搜索,,,,,,,,,,,
分类,,,遗传算法,,,,,,,,,,
知识点,,,,染色体,,,,,,,,,
分类,,,,适应度,,,,,,,,,
知识点,,,,,适应度函数,,,,,,,,
知识点,,,,种群,,,,,,,,,
分类,,,,遗传操作,,,,,,,,,
分类,,,,,选择-复制,,,,,,,,
知识点,,,,,,选择概率,,,,,,,
知识点,,,,,交叉,,,,,,,,
知识点,,,,,变异,,,,,,,,
分类,,,,遗传算法过程,,,,,,,,,
知识点,,,,,"(1)在论域空间U 上定义一个适应度函数f(x), 给定种群规模N, 交叉率P。和变异率Pm, 代数T。",,,,,,,,
知识点,,,,,"(2)随机产生U 中 的N 个染色体s₁,S₂,…,sn, 组成初始种群S={s₁,s₂,…,sn},  置代数计数器t=1。",,,,,,,,
知识点,,,,,(3)计算S 中每个染色体的适应度f(x)。,,,,,,,,
知识点,,,,,(4)若终止条件满足，则取S 中适应度最大的染色体作为所求结果，算法结束。,,,,,,,,
知识点,,,,,(5)按选择概率P(x₁) 所决定的选中机会，每次从S 中随机选定1个染色体并将其复制，共做N 次，然后将复制所得的N 个染色体组成群体S₁。,,,,,,,,
知识点,,,,,"(6)按交叉率P。所决定的参加交叉的染色体数c, 从 S₁ 中随机确定c 个染色体，配对进行交叉操作，并 用产生的新染色体代替原染色体，得群体S₂。",,,,,,,,
知识点,,,,,"(7)按变异率P    所决定的变异次数m, 从S₂  中随机确定m 个染色体，分别进行变异操作，并用产生的 新染色体代替原染色体，得群体Sg。",,,,,,,,
知识点,,,,,"(8)将群体S₃  作为新一代种群，即用S₃   代 替S,t=t+1,  转步骤(3)。",,,,,,,,
分类,,,,遗传算法特点,,,,,,,,,
知识点,,,,,遗传算法一般是直接在解空间搜索，而不像图搜索那样一般是在问题空间搜索， 最后才找到解(如果搜索成功的话),,,,,,,,
知识点,,,,,遗传算法的搜索随机地始于搜索空间的一个点集，而不像图搜索那样固定地始 于搜索空间的初始节点或终止节点。所以，遗传算法是一种随机搜索算法。,,,,,,,,
知识点,,,,,"遗传算法总是在寻找优解(最优解或次优解),而不像图搜索那样并非总是要求 优解，而一般是设法尽快找到解(当然包括优解)。所以，遗传算法又是一种优化搜索 算法。",,,,,,,,
知识点,,,,,遗传算法的搜索过程是从空间的一个点集(种群)到另一个点集(种群)的搜索， 而不像图搜索那样一般是从空间的一个点到另一个点的搜索。因而它实际是一种并行搜 索，适合大规模并行计算，而且这种种群到种群的搜索有能力跳出局部最优解。,,,,,,,,
知识点,,,,,遗传算法的适应性强，除需知适应度函数外，几乎不需要其他先验知识。,,,,,,,,
知识点,,,,,遗传算法长于全局搜索，它不受搜索空间的限制性假设的约束，不要求连续性， 能以很大的概率从离散的、多极值的、含有噪声的高维问题中找到全局最优解。,,,,,,,,
